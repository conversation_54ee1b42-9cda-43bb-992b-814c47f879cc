#!/usr/bin/python
# -*- coding: utf-8 -*-

import pexpect
import sys
from dotenv import load_dotenv
import os
import re
import time

load_dotenv()

def removewhitelist(olt, serial):
  try:   
    child = pexpect.spawn('telnet {}'.format(olt), encoding='utf-8')
    # Habilita o debug (retorno na tela)
    #child.logfile = sys.stdout
    child.expect('Login: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_USER'))
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('User> ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline("enable")
    child.expect('Password: ', int(os.getenv('PEXPECT_TIMEOUT')))
    child.sendline(os.getenv('CLI_PASS'))
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    # verifica se e rp1000
    child.sendline('dir')
    child.expect('Admin# ', int(os.getenv('PEXPECT_TIMEOUT')))
    ret = ""
    ret += child.before
    rp1000 = ret.find('<DIR>') != -1
    ######################
    if (rp1000):
      child.sendline('cd onu')
      expectstr = 'onu#'  
    else:
      child.sendline('cd gpononu')
      expectstr = 'gpononu#'

    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))

    #padrao physicid = quatro primeiros caracteres maiusculos e o restante minusculo
    serial = serial[:4].upper()+serial[4:].lower()

    child.sendline('set whitelist phy_addr address {} password null action delete'.format(serial))

    child.expect(expectstr, int(os.getenv('PEXPECT_TIMEOUT')))
    data = child.before.replace(' ', '')

    result = 'success' in data or 'setonuwhitelistok' in data
    return True
  except:
    return False  
