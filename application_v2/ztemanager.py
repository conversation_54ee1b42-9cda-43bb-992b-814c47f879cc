from netmiko import ConnectHandler
import re
import time
from typing import Optional, List, Dict, Union
from dotenv import load_dotenv
import os
import logging

# Enable logging
#logging.basicConfig(level=logging.DEBUG)
#logger = logging.getLogger("netmiko")

"""
Classe refatorada parcialmente usando DeepSeek 

Classe ZTEManager
Gerencia conexões e comandos em OLTs ZTE usando Netmiko.
Esta classe permite conexão SSH, envio de comandos, e manipulação de ONUs em OLTs ZTE.

A classe suporta operações como listar ONUs autorizadas, desativar paginação, e obter informações de PONs.
Ela também implementa um gerenciador de contexto para garantir desconexão adequada.

"""

class ZTEManager:
    def __init__(self, host: str, port: int = 22, name=''):
        load_dotenv()
        """
        Inicializa o gerenciador ZTE.
        
        Args:
            host: Endereço IP da OLT
            username: Nome de usuário
            password: Senha
            secret: Senha do modo enable (opcional)
            port: Porta SSH (padrão 22)
        """
        self.name = name

        self.connection_params = {
            'device_type': 'zte_zxros',  # Tipo específico para ZTE
            'host': host,
            'username': os.getenv('OLT_ZTE_USERNAME'),
            'password': os.getenv('OLT_ZTE_PASSWORD'),
            'secret': os.getenv('OLT_ZTE_PASSWORD'),
            'port': port,
            'global_cmd_verify': False,
            'fast_cli': False
        }
        self.connection = None
        self.prompt_pattern = r'[>#]\s*$'  # Padrão para prompt ZTE

    def connect(self) -> bool:
        """Estabelece conexão SSH com a OLT"""
        try:
            self.connection = ConnectHandler(**self.connection_params)
            self._determine_prompt()
            return True
        except Exception as e:
            print(f"Falha na conexão: {str(e)}")
            return False

    def _determine_prompt(self):
        """Identifica o prompt dinamicamente"""
        self.connection.write_channel("\n")
        time.sleep(1)
        initial_output = self.connection.read_channel()
        self.prompt_pattern = re.escape(initial_output.strip()) + r'\s*$'

    def disconnect(self):
        """Encerra a conexão"""
        if self.connection:
            self.connection.disconnect()

    def send_command(self, command: str, timeout: int = 3) -> str:
        """
        Envia comando e retorna a saída completa.
        
        Args:
            command: Comando a ser executado
            timeout: Tempo máximo de espera (segundos)
        """
        if not self.connection:
            raise ConnectionError("Conexao nao estabelecida")

        self.connection.write_channel(command + "\n")
        return self._read_until_done(timeout)

    def _read_until_done(self, timeout: int) -> str:
        """Lê a saída até detectar o prompt ou timeout"""
        output = ""
        start_time = time.time()
        
        while (time.time() - start_time) < timeout:
            time.sleep(0.5)
            chunk = self.connection.read_channel()
            
            if chunk:
                output += chunk
                
                # Verifica se o prompt apareceu
                if re.search(self.prompt_pattern, output, flags=re.MULTILINE):
                    break
                
                # Verifica erros comuns ZTE
                if "% Invalid input" in output or "% Incomplete command" in output:
                    break

        # Processamento da saída
        output = re.sub(r'^.*?\n', '', output)  # Remove eco do comando
        output = re.sub(r'\n\s*\n', '\n', output)  # Remove linhas vazias extras
        return output.strip()

    def disable_paging(self):
        """Desativa paginação de saída"""
        return self.send_command("terminal length 0")

    def find_auth(self, sn):
        """ Find authorized ONU 

        Parameters
        ----------
        sn : str
            ONU serial number

        Returns
        -------
        dict
            Authorized ONU

        Raises
        ------
        Error

        """
        try:
            cmd = (f"configure terminal\n"
                   f"show gpon onu by sn {serial}\n"
                )
            output = self.send_command(cmd)

            # Padrão regex para extrair as informações da ONU
            pattern = r"gpon_onu-(\d+)/(\d+)/(\d+):(\d+)"
    
            # Verifica se há correspondência no output
            match = re.search(pattern, output)
    
            # Se a ONU já estiver provisionada, desautoriza
            if match:
                onu = {
                        'chassi': int(match.group(1)),
                        'slot': int(match.group(2)),
                        'pon': int(match.group(3)),
                        'onuid': int(match.group(4))
                    }
                return onu
                
        except Exception as e:
            return "Error (find_auth): "+str(e)

    def deauth(self, sn=None, chassi=None, slot=None, pon=None, onuid=None):
        """ Deauthorize ONU 

        Parameters
        ----------
        sn : str
            ONU serial number
        """
        try:
            if(sn):
                onu = self.find_auth(sn)
            else:
                if(all([chassi, slot, pon, onuid])):
                    onu = {
                        "chassi": chassi,
                        "slot" : slot,
                        "pon" : pon,
                        "onuid" : onuid
                    }    

            if(onu):
                cmd = (f"configure terminal\n"
                f"interface gpon_olt-{onu['chassi']}, {onu['slot']}, {onu['pon']}\n"
                f"no onu {onu['onuid']}\n"
                f"exit"
                )
                output = self.send_command(cmd)

                return True
            else:
                return False    

        except Exception as e:
            return "Error (unauth): " + str(e)

    def provision(self, chassi, slot, pon, serial, type, name, vlan_tr069, vlan_pppoe, vlan_iptv_unicast, vlan_iptv_multicast, bridge=False):
        """Provisiona ONU"""
        
        try:
            # Verifica se o type da ONU foi informado
            if "N/A" in type:
                raise Exception("Sem informacoes do type da ONU")

            # Mapeamento de padrões para modelos
            modelo_map = {
                '670': 'F670L',
                '6600': 'F6600P',
                '660': 'F660',
                'ZS': 'AN5506-02-B',
                'WK': 'AN5506-02-B',
                'GJ': 'AN5506-02-B',
                'HX': 'AN5506-02-B',
                'WJ': 'AN5506-02-B',
                'LL': 'AN5506-02-B',
                'DZ': 'AN5506-04-A1',
                'HG': 'AN5506-04-A1'
            }
    
            # Procura por correspondência nos padrões
            for padrao, modelo in modelo_map.items():
                if padrao in type:
                    type = modelo
                    break
    

            fiberhome = 'AN5506' in type

            #Verifica se a ONU ja esta provisionada
            cmd = (f"configure terminal\n"
                   f"show gpon onu by sn {serial}\n"
                )
            output = self.send_command(cmd)

            # Padrão regex para extrair as informações da ONU
            pattern = r"gpon_onu-(\d+)/(\d+)/(\d+):(\d+)"
    
            # Verifica se há correspondência no output
            match = re.search(pattern, output)
    
            #show collection pon-onu gpon_onu-1/1/1:1 config

            # Se a ONU já estiver provisionada, desautoriza
            if match:
                # Extrai os componentes
                prev_chassi = match.group(1)
                prev_slot = match.group(2)
                prev_pon = match.group(3)
                prev_onuid = match.group(4)

                # Desautoriza a ONU
                cmd = (f"interface gpon_olt-{prev_chassi}/{prev_slot}/{prev_pon}\n"
                       f"no onu {prev_onuid}\n"
                       f"exit\n"
                )
                output = self.send_command(cmd)

            # Gera um ID para a ONU
            cmd = (f"configure terminal\n"
                f"show gpon onu state gpon_olt-{chassi}/{slot}/{pon}\n"
                f"exit"
                )
            output = self.send_command(cmd)
            
            used_ids = set()
            onuid = 0
            
            for line in output.splitlines():
                port=f"{chassi}/{slot}/{pon}"
                # Match lines with port:ID pattern (e.g., "1/1/1:1")
                if re.match(rf"{re.escape(port)}:\d+", line.strip()):
                    onu_id = int(line.split(':', 1)[1].split()[0])
                    used_ids.add(onu_id)

            # Find first available ID (1-128)
            for candidate in range(1, 129):  # 128 is max per port
                if candidate not in used_ids:
                    onuid = candidate
                    break
            
            #Autoriza a ONU 
            cmd = (f"configure terminal\n"
                f"interface gpon_olt-{chassi}/{slot}/{pon}\n"
                f"onu {onuid} type {type} sn {serial}\n"
            )
            output = self.send_command(cmd)

            if "Error 140303: Invalid input detected" in output:
                raise Exception("Entrada inválida para chassi/slot/pon/id/serial")

            if "already exists" in output:
                raise Exception("ONU ja existe")
                
            if "Not support this ONU" in output:
                raise Exception("ONU nao suportada")

            cmd = (f"exit\n"
                f"interface gpon_onu-{chassi}/{slot}/{pon}:{onuid}\n"
                f"name {name}\n"
                f"vport-mode manual\n"
                f"tcont 1 profile TELEMIDIA-NAVEGACAO\n"
                f"tcont 2 profile TELEMIDIA-IPTV\n"
                f"gemport 1 tcont 1\n"
                f"gemport 2 tcont 2\n"
                f"gemport 3 tcont 1\n"
                f"gemport 4 tcont 2\n"
                f"vport 1 map-type vlan\n"
                f"vport-map 1 1 vlan {vlan_tr069}\n"
                f"vport-map 1 2 vlan {vlan_iptv_multicast}\n"
                f"vport-map 1 3 vlan {vlan_pppoe}\n"
                f"vport-map 1 4 vlan {vlan_iptv_unicast}\n"
                f"exit\n"
                f"pon-onu-mng gpon_onu-{chassi}/{slot}/{pon}:{onuid}\n"
                f"service 1 gemport 1 vlan {vlan_tr069}\n"
                f"service 2 gemport 2 vlan {vlan_iptv_multicast}\n"
                f"service 3 gemport 3 vlan {vlan_pppoe}\n"
                f"service 4 gemport 4 vlan {vlan_iptv_unicast}\n"
                f"mvlan {vlan_iptv_multicast}\n"
                )

            if(bridge==False):
                cmd+=(f"vlan port eth_0/2 mode tag vlan {vlan_iptv_unicast}\n"
            )

            #se for onu Fiberhome ou onu em bridge, marca a vlan pppoe na porta 1
            if(fiberhome==True or bridge==True):
                cmd+=(f"vlan port eth_0/1 mode tag vlan {vlan_pppoe}\n"
            )
                
            cmd+=(f"exit\n"
                f"interface vport-1/1/1.{onuid}:1\n"
                f"service-port 1 user-vlan {vlan_tr069} vlan {vlan_tr069}\n"
                f"service-port 2 user-vlan {vlan_iptv_multicast} vlan {vlan_iptv_multicast}\n"
                f"service-port 3 user-vlan {vlan_pppoe} vlan {vlan_pppoe}\n"
                f"service-port 4 user-vlan {vlan_iptv_unicast} vlan {vlan_iptv_unicast}\n"
                f"igmp fast-leave enable\n"
                f"igmp version v2\n"
                f"exit\n"
                f"igmp mvlan {vlan_iptv_multicast}\n"
                f"receive-port vport-1/1/1.{onuid}:1\n"
                f"exit\n"
                )

            output = self.send_command(cmd)

            return True

            '''
            return {
                'status': 'successs',
                'message': 'Provisionamento concluido com sucesso',
                'parameters': {
                    'chassi': chassi, 
                    'slot': slot, 
                    'pon': pon, 
                    'serial': serial, 
                    'onuid': onuid,
                    'type': type, 
                    'name': name, 
                    'vlan_tr069':vlan_tr069, 
                    'vlan_pppoe':vlan_pppoe, 
                    'vlan_iptv_unicast':vlan_iptv_unicast, 
                    'vlan_iptv_multicast':vlan_iptv_multicast, 
                    'fiberhome':fiberhome
                }
            }
            '''
            
        except Exception as e:
                    return {
                        'status': 'error',
                        'message': str(e),
                        'parameters': {
                            'chassi': chassi, 
                            'slot': slot, 
                            'pon': pon, 
                            'serial': serial, 
                            'type': type, 
                            'name': name, 
                            'vlan_tr069':vlan_tr069, 
                            'vlan_pppoe':vlan_pppoe, 
                            'vlan_iptv_unicast':vlan_iptv_unicast, 
                            'vlan_iptv_multicast':vlan_iptv_multicast, 
                            'fiberhome':fiberhome
                        }
                    } 
            
    def list_unauth(self):
        """ Retrieve unauthorized ONUs 

        Returns
        -------
        dict
            Unauthorized ONUs list

        Raises
        ------
        Error

        """
        try:
            unauthorized = []
            cmd = (
                f"show pon onu uncfg\n"
                f"end"
            )
            
            res = self.send_command(cmd)

            # localiza a linha pontilhada que separa a lista de onus nao autorizadas
            if '---' in res:
                res = res.splitlines()
                index = next(i for i, w in enumerate(res) if '---' in w)

                for data in res[index+1:]:
                    # normaliza a string deixando com apenas um espaco por valor
                    data = ' '.join(data.split())
                    # divide os valores em lista, separando cada valor por espaco
                    data = data.split(' ')
                    if(len(data)) == 4:
                        type = data[1]
                        serial = data[2]
                        chassi = int(data[0].split('/')[0].split('-')[1])
                        slot = int(data[0].split('/')[1])
                        pon = int(data[0].split('/')[2])
                        onu = {
                            'olt_ip': self.connection_params['host'],
                            'olt_name': self.name,
                            'olt_model': 'ZTE',
                            'chassi': chassi,
                            'slot': slot,
                            'pon': pon,
                            'serial': serial,
                            'type': type
                        }
                        unauthorized.append(onu)
            return unauthorized
        except Exception as e:
            return "Error (list_unauth): "+str(e)

    def list_auth(self, chassi: int, slot: int, pon: int) -> dict:
        """List authorized ONUs in a specific PON port with enhanced parsing
        
        Parameters
        ----------
        chassi : int
            OLT chassi number (1-16)
        slot : int
            OLT slot number (1-16)
        pon : int
            OLT PON port number (1-16)
        
        Returns
        -------
        dict
            {
                'onus': list[dict],  # List of authorized ONUs
                'first_id': int,      # First ONU ID
                'last_id': int,       # Last ONU ID
                'next_id': int,      # Next available ID
                'missing_ids': list[int]  # List of missing IDs
            }
        
        Raises
        ------
        ValueError
            If invalid parameters are provided
        ConnectionError
            If communication with OLT fails
        """
        # Validate input parameters
        if not all(1 <= x <= 16 for x in (chassi, slot, pon)):
            raise ValueError("Invalid parameter. Chassi, slot and PON must be between 1-16")

        try:
            # Using more reliable command structure
            cmd = (
                f"configure terminal\n"
                f"interface gpon_olt-{chassi}/{slot}/{pon}\n"
                f"show this\n"
                f"end"
            )
            
            res = self.send_command(cmd)
            
            # Improved parsing with error handling
            xpon_start = res.find('!<xpon>')
            xpon_end = res.find('!</xpon>')
            
            if xpon_start == -1 or xpon_end == -1:
                return {
                    'onus': [],
                    'first_id': None,
                    'last_id': None,
                    'next_id': 1,
                    'missing_ids': []
                }

            # Extract and clean ONU list
            onu_block = res[xpon_start+7:xpon_end].strip()
            onu_lines = [line.strip() for line in onu_block.splitlines() if line.strip()]
            
            # Enhanced ONU parsing with validation
            onu_pattern = re.compile(
                r"onu\s+(?P<id>\d+)\s+type\s+(?P<type>\S+)\s+sn\s+(?P<sn>\S+)"
            )
            
            onu_list = []
            valid_ids = set()
            
            for line in onu_lines:
                match = onu_pattern.search(line)
                if match:
                    try:
                        onu_id = int(match.group('id'))
                        onu_list.append({
                            'id': onu_id,
                            'type': match.group('type'),
                            'sn': match.group('sn'),
                        })
                        valid_ids.add(onu_id)
                    except (ValueError, IndexError):
                        continue
            
            # Calculate ID statistics
            if not onu_list:
                return {
                    'onus': [],
                    'first_id': None,
                    'last_id': None,
                    'next_id': 1,
                    'missing_ids': []
                }
            
            min_id = min(valid_ids)
            max_id = max(valid_ids)
            all_ids = set(range(min_id, max_id + 1))
            missing_ids = sorted(all_ids - valid_ids)
            
            # Determine next available ID
            next_id = min_id if min_id > 1 else (
                missing_ids[0] if missing_ids else max_id + 1
            )
            
            return {
                'onus': sorted(onu_list, key=lambda x: x['id']),
                'first_id': min_id,
                'last_id': max_id,
                'next_id': next_id,
                'missing_ids': missing_ids,
                'total': len(onu_list)
            }
            
        except Exception as e:
            #self._log_error(f"Failed to list authorized ONUs: {str(e)}")
            raise ConnectionError(f"Failed to retrieve ONU list: {str(e)}") from e
    
    
    def show_config_onu(
        self,
        sn: str = None,
        chassi: int = None,
        slot: int = None,
        pon: int = None,
        onu_id: int = None,
        start_id: int = None,
        end_id: int = None
    ) -> Union[dict, List[dict]]:
        """Retrieve ONU configuration(s) with support for single ONU or range of IDs
        
        Parameters
        ----------
        sn : str, optional
            ONU serial number
        chassi : int, optional
            OLT chassi number (1-16)
        slot : int, optional
            OLT slot number (1-16)
        pon : int, optional
            OLT PON port number (1-16)
        onu_id : int, optional
            Single ONU ID (1-128)
        start_id : int, optional
            First ONU ID in range
        end_id : int, optional
            Last ONU ID in range
        
        Returns
        -------
        Union[dict, List[dict]]
            - Single ONU: dict with configuration
            - Range of ONUs: list of dicts
            - Error: dict with error details
        
        Raises
        ------
        ValueError
            If invalid parameters are provided
        """
        # Validate parameters
        if not sn and not all([chassi, slot, pon]):
            raise ValueError("Must provide either SN or complete location (chassi/slot/pon)")
        
        if not any([onu_id, start_id, sn]):
            raise ValueError("Must provide either onu_id, start_id or sn")
        
        # Validate ranges
        for param, value, max_val in [
            ('chassi', chassi, 16),
            ('slot', slot, 16),
            ('pon', pon, 16),
            ('onu_id', onu_id, 128),
            ('start_id', start_id, 128),
            ('end_id', end_id, 128)
        ]:
            if value is not None and not 1 <= value <= max_val:
                raise ValueError(f"{param} must be between 1-{max_val}")

        # Check for valid range
        if start_id is not None and end_id is not None and start_id > end_id:
            raise ValueError("start_id must be less than or equal to end_id")

        try:
            # Get ONU location if only SN is provided
            if sn:
                onu_info = self.find_auth(sn)
                if not onu_info:
                    return {
                        'status': 'error',
                        'message': f'ONU with SN {sn} not found',
                        'sn': sn
                    }
                
                chassi = onu_info.get('chassi')
                slot = onu_info.get('slot')
                pon = onu_info.get('pon')
                onu_id = onu_info.get('id')
                start_id = None  # Clear range if SN is specified
                end_id = None

            # Prepare base command parts
            base_cmd = [
                "terminal length 0",
                "configure terminal",
                f"interface gpon_olt-{chassi}/{slot}/{pon}"
            ]

            # Determine ONU IDs to check
            if onu_id:  # Single ONU mode
                onu_ids = [onu_id]
            elif start_id and end_id:  # Range mode
                onu_ids = range(start_id, end_id + 1)
            else:
                raise ValueError("Invalid ONU ID specification")

            results = []
            
            # Send base commands once
            for cmd in base_cmd:
                self.send_command(cmd)

            # Process each ONU
            for current_id in onu_ids:
                try:
                    cmd = f"show collection pon-onu gpon_onu-{chassi}/{slot}/{pon}:{current_id} config"
                    output = self.send_command(cmd)
                    
                    #parsed = self._parse_onu_config(output)
                    results.append({
                        'status': 'success',
                        'chassi': chassi,
                        'slot': slot,
                        'pon': pon,
                        'onu_id': current_id,
                        'raw_config': output
                    })
                except Exception as e:
                    results.append({
                        'status': 'error',
                        'message': str(e),
                        'onu_id': current_id
                    })

            # Send exit commands
            self.send_command("exit")
            self.send_command("end")

            # Return appropriate response format
            if onu_id:  # Single ONU mode
                return results[0] if results else {
                    'status': 'error',
                    'message': 'No configuration found'
                }
            return results  # Range mode

        except Exception as e:
            self._log_error(f"Failed to retrieve ONU config: {str(e)}")
            return {
                'status': 'error',
                'message': str(e),
                'parameters': {
                    'sn': sn,
                    'chassi': chassi,
                    'slot': slot,
                    'pon': pon,
                    'onu_id': onu_id,
                    'start_id': start_id,
                    'end_id': end_id
                }
            }  

    def __enter__(self):
        """Suporte para context manager (with statement)"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Garante desconexão ao sair do contexto"""
        self.disconnect()