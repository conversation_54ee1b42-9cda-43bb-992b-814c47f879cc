#!/usr/bin/python
# -*- coding: utf-8 -*-
from dotenv import load_dotenv
import os
import time
from database import *
from multiprocessing import Process
import time
import sys
from datetime import datetime
from ztemanager import ZTEManager
import threading
import uuid
from tasks import create_provision
#from genieacs import *
#from worker_provision_queue import conn
#from tasks_provision_zte import *

load_dotenv()

scriptname = 'DISCOVERY'

#regra de modelo/plano
def plan_rule(model, plan, username):

    try:
        '''
        Regras ZTE				
        ==========
        Cliente	    Plano	    ONU Nova	ONU Antiga	Acao
        Novo*	    50	        F660	    null	    Provisionar
        Antigo**	50	        F660	    F660	    Provisionar
        Todos	    >50 & <100	F660	    Todos	    Provisionar
        Todos	    >=100	    F670	    Todos	    Provisionar
        
        * uma alocacao
        ** mais de uma alocacao
        '''
        valid = None
        f660 = '660' in model and '6600' not in model
        f670 = '670' in model
        f6600 = '6600' in model

        if(not f660 and not f670 and not f6600):
            return None

        if(int(plan) > 50 and int(plan) < 100):
            if(f670 or f6600):
                valid = 'F660'
            if(f660):
                return None    

        if int(plan) >= 100:
            if(f660):
                valid = 'F670'
            if(f670 or f6600):
                return None    

        if valid == None:
            onus = get_onus_alocadas(username)
            #Novo
            if(len(onus) <= 1):
                if(f670):
                    valid = 'F660'
            #Antigo
            else:
                anterior = onus[0]['descricao']
                if(f670):
                    if '660' in anterior and not '6600' in anterior:
                        valid = 'F660'
                if(f660):
                    if '670' in anterior:
                        valid = 'F670'
                    elif not '660' in anterior:    
                        valid = 'FIBERHOME'

        return valid
    except:
        return False  

#verifica se a onu possue todos os dados para iniciar o provisionamento
def checkonu(onu):
  try:
    missing_params = []

    # obrigatorio patrimonio
    if(not ('patrimonio' in onu and onu['patrimonio'])):
        missing_params.append('PATRIMONIO')
    
    # Libera apenas se tiver a alocacao do comodato e nao esteja na tabela ignore com o parametro comodato=1
    if(not('id_comodato' in onu and onu['id_comodato']) and not(checkignorelist(serial=onu['serial'], comodato=1)) and not(checkignorelist(username=onu['username'], comodato=1))):
        missing_params.append('ALOCACAO COMODATO')
    
    # Libera apenas se tiver o modelo e nao esteja na tabela ignore com o parametro modelo=1
    if(not('modelo' in onu and onu['modelo'])and not(checkignorelist(serial=onu['serial'], modelo=1))):
        missing_params.append('MODELO')

    # obrigatorio username
    if(not('username' in onu and onu['username'])):
        missing_params.append('USERNAME')

    # obrigatorio mac
    if(not('mac' in onu and onu['mac'])):
        missing_params.append('MAC')

    # obrigatorio login ativo
    if(not('login_ativo' in onu and onu['login_ativo'] == 'S')):
        missing_params.append('LOGIN ATIVO')

    # obrigatorio pacote wifi para onus zte que nao estejam na lista para ignorar
    if('username' in onu and onu['username'] and 'ZTE' in onu['serial'].upper() and not(checkignorelist(serial=onu['serial'], pacote_wifi=1)) and not(checkignorelist(username=onu['username'], pacote_wifi=1)) and not(get_pacotewifi(onu['username']))):
        missing_params.append('PACOTE WIFI')

    # verifica se o modelo da onu e compativel com o plano.
    if('plano' in onu and 'modelo' in onu and 'username' in onu and onu['username'] and not(checkignorelist(serial=onu['serial'], plano=1)) and not(checkignorelist(username=onu['username'], plano=1))):
        pending = plan_rule(onu['modelo'], onu['plano'], onu['username'])
        if(pending):
            missing_params.append('ONU INCOMPATIVEL. NECESSARIO {}'.format(pending))

    return missing_params
    
  except Exception as e:
    raise

# Faz as checagens da lista de onus nao autorizadas
# e adiciona na fila de provisionamento caso atenda os requisitos 
def check_list(unauth_list):

  for unauth in unauth_list:

        onu = {
            'olt': unauth["olt_name"],
            'olt_ip': unauth["olt_ip"],
            'olt_model': 'ZTE',
            'chassi' : unauth['chassi'],
            'slot': unauth['slot'],
            'pon': unauth['pon'],
            'placa': unauth['slot'],
            'porta': unauth['pon'],
            'serial': unauth['serial'],
            'source': scriptname
        }

        
        onu['onu_type'] = unauth['type']
        onu['modelo'] = unauth['type']
        onu['data'] = datetime.now()
        onu['mac'] = None
        onu['login'] = None 
        onu['patrimonio'] = None 
        onu['login_ativo'] = None
        onu['data_comodato'] = None
        onu['id_comodato'] = None
        onu['status'] = 'WAITING'

        # consulta se a onu esta alocada
        patrimonio = getpatrimonio_alocado(onu['serial']) 

        if(patrimonio):
            onu['mac'] = patrimonio['mac']
            onu['patrimonio'] = patrimonio['patrimonio']
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['data_comodato'] = patrimonio['data_comodato']
            onu['id_comodato'] = patrimonio['id_comodato']
            onu['username'] = patrimonio['login']  
            onu['login_ativo'] = patrimonio['login_ativo']
            onu['plano'] = patrimonio['plano']

            if('ZTE' in onu['serial'].upper()):
                onu['tr069'] = 1
            else:
                onu['tr069'] = 0  

            if((os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None) and (checkignorelist(serial=onu["serial"].upper()))):
                msg = f"[{scriptname}] ONU {onu['serial']} esta na lista para ignorar e nao sera autorizada"
                print(msg)
            else:
              #verifica se ja existe um provisionamento em andamento 
              if(pending_provision(onu['serial'])):
                print(f"[{scriptname}] Provisionamento pendente para {onu['serial']}")
              else:
                #verifica se a onu atende os requisitos para o provisionamento
                missing_params = checkonu(onu)

                if(len(missing_params) == 0):
                  #se for informada uma onu de testes, provisiona apenas esta onu
                  if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    print(f"[{scriptname}] ONU {onu['serial'].upper()} sera provisionada")
                    print(f"[{scriptname}] Dados da ONU {onu['serial'].upper()}: {onu}")
                    result = create_provision(onu)
                    print(f"[{scriptname}] Jobs criados para a ONU {onu['serial'].upper()}: {result}")
                else:
                  current_provision= {
                    'id' : uuid.uuid4().hex, 
                    'serial': onu.get('serial'),
                    'username' : onu.get('username'),
                    'source': onu.get('source'),
                    'olt': onu.get('olt'),
                    'olt_ip': onu.get("olt_ip"),
                    'olt_model': 'ZTE',
                    'slot': onu.get('placa'),
                    'pon': onu.get('porta'),
                    'model': onu.get('modelo'),
                    'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                    'exc_info': f"Faltando parametros: {missing_params}",
                    'enqueued_at': datetime.now()
                  }
                  if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                    #provision_id = update_provision(current_provision)
                    print(f"[{scriptname}] {onu['serial'].upper()} Faltando parametros {missing_params}")
        else:
            current_provision= {
                    'id' : uuid.uuid4().hex, 
                    'serial': onu['serial'],
                    'username' : None,
                    'source': onu['source'],
                    'olt': onu['olt'],
                    'olt_ip': onu["olt_ip"],
                    'olt_model': 'ZTE',
                    'slot': onu['placa'],
                    'pon': onu['porta'],
                    'model': None,
                    'status': 'canceled', #queued, started, deferred, finished, stopped, scheduled, canceled, failed
                    'exc_info': 'ONU sem patrimônio',
                    'enqueued_at': datetime.now()
            }
            if(((os.getenv('ONU_TESTES')) and (onu['serial'].upper() == os.getenv('ONU_TESTES'))) or (os.getenv('ONU_TESTES') == '' or os.getenv('ONU_TESTES') == None)): 
                #provision_id = update_provision(current_provision)
                msg = f"[{scriptname}] ONU {onu['serial'].upper()} sem patrimonio"
                print(msg)


def getdiscovery(olt):
    
    with ZTEManager(
        host=olt['ip'],
        name=olt['name'],
    ) as manager:
      manager.disable_paging()
      onus = manager.list_unauth()

      #encaminha para o processo de checagem
      if(len(onus) > 0):
        print(f"{len(onus)} onu(s) em {olt['name']} ({olt['ip']}) encaminhada(s) para a checagem")
        check_list(onus)
      else:
        print(f"Nenhuma onu aguardando provisionamento em {olt['name']} ({olt['ip']})")

      manager.disconnect()
      del manager

if __name__ == '__main__':

  try:
    # lista de olts zte habilitadas na tabela monitors com o task_group definido
    olts = monitors('ZTE')

    proccess = []

    for olt in olts:
      # inicia um processo para cada olt
      p = Process(target=getdiscovery, args=(olt,))
      p.start()
      proccess.append(p)
  except Exception as e:
    print('Ocorreu um erro em __main__: '+str(e))

