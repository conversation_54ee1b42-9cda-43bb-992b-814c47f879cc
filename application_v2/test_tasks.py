from tasks import *
from dotenv import load_dotenv
from database import get_provision_tasks, update_job, updatedevice, update_provision, getvlans, getdevice, findauthorizedonu
import uuid

load_dotenv()
'''
Onu de teste
item = {'serial': 'ZTEGD14E85A3',
        'username': 'testes.f6600',
        'olt_ip': '*************',
        'olt': 'OLT And Homologacao',
        'olt_model': 'ZTE',
        'chassi': 1,
        'placa': 1,
        'porta': 1,
        'mac': 'A0:09:2E:EF:D7:2B',
        'modelo': 'F6600P',
        'source': 'teste'
}     
'''

item = {
	"chassi": 1,
	"olt_ip": "**********",
	"porta": 6,
	"placa": 1,
        "serial": "ZTEGD1E9F790",
        "mac": "40:0E:F3:11:20:46",
        "username": "renatodepa<PERSON><PERSON>",
        "olt": "OLT Cps ZTE_OsvaldoCruz_01",
        "olt_model": "ZTE",
        "modelo": "F6600P",
        "source": "teste",
}

'''
item = {'serial': 'ZTEGCC4D3ACC',
        'username': 'testes.f660',
        'olt_ip': '*************',
        'olt': 'OLT And Homologacao',
        'olt_model': 'ZTE',
        'placa': 1,
        'porta': 1,
        'mac': '8C:DC:02:9D:B5:D4',
        'modelo': 'F660',
        'source': 'teste'
}     
'''

'''
filters = [
    ("serial", "=", item['serial']),
    ("OR", [
        [("chassi", "!=", item['chassi'])],
        [("slot", "!=", item['placa'])],
        [("pon", "!=", item['porta'])],
        [("olt_ip", "!=", item['olt_ip'])],
    ])
]


previous_device = getdevice(filters)
print(len(previous_device))
'''

#precondition = "len(findauthorizedonu(serial)) > 0"

#context = {"serial": "FHTT11cbf340"}
#functions = {"findauthorizedonu": findauthorizedonu}


'''
filters = [
           ("serial", "=", item['serial']),
           ("OR", [
            [("slot", "!=", item['placa'])],
            [("pon", "!=", item['porta'])],
            [("olt_ip", "!=", item['olt_ip'])],
           ])
        ]

previous_device = getdevice(filters)
print(len(previous_device))

context = {"filters": filters}
functions = {"getdevice": getdevice}
precondition = "len(getdevice(filters)) > 0"

result = evaluate_precondition(precondition, context, functions)
if not result:
    raise Exception("Precondition not met")
#print(result)  # True
'''

'''
precondition = "len(findauthorizedonu(serial)) > 0"

context = {"serial": item['serial']}
functions = {"findauthorizedonu": findauthorizedonu}

result = evaluate_precondition(precondition, context, functions)
if not result:
    raise Exception("Precondition not met")


#print(findauthorizedonu(serial=item['serial']))

'''

'''
item['vlan_dhcp'] = os.getenv('TR069_DHCP_VLANID')
vlans = getvlans(item['olt_ip'], item['placa'], item['porta'])

if not vlans:
   raise Exception("Erro ao obter vlans")
    
item["vlan_pppoe"] = int(vlans['vlan_pppoe'])
item["vlan_iptv"] = int(vlans['vlan_iptv'])


#job_id = str(uuid.uuid4())

#task_provision(job_id, **item)

result = create_provision(item)
print(result)
'''



'''
tasks = get_provision_tasks(olt=item["olt_ip"])
for task in tasks:
        print(task['precondition'])
'''        
with ZTEManager(
        host='**********',
        name='OLT Cps ZTE_OsvaldoCruz_01',
) as manager:
       manager.disable_paging()
       response = manager.list_unauth()
       print(response)
       

