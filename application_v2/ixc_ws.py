#!/usr/bin/python
# -*- coding: utf-8 -*-
import requests
import base64
import json
import os
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def update_dados_tecnicos(payload):

    try:
        host = os.getenv('IXC_WEBSERVICE')
        url = "https://{}/webservice/v1/radusuarios/{}".format(host, payload['id'])
        token = os.getenv('IXC_TOKEN').encode('utf-8')

        headers = {
            'ixcsoft': '',
            'Authorization': 'Basic {}'.format(base64.b64encode(token).decode('utf-8')),
            'Content-Type': 'application/json; charset=utf-8'
        }

        payload['interface_transmissao_fibra'] = payload['interface_transmissao']

        #corrige problema na codificacao
        payload = json.dumps(
            payload, indent=1, ensure_ascii=False)
        payload = payload.encode('utf-8').decode('latin-1')
        payload = json.loads(payload)
        #################################

        response = requests.put(url, json=payload, headers=headers, verify=False)

        return(response.text)
    except Exception as e:
        print("Ocorreu um erro em update_dados_tecnicos: {0}".format(e))
        raise