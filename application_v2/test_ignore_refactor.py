#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Teste para verificar a refatoração da função checkignorelist para get_ignored_params
"""

from database import get_ignored_params, checkignorelist
from discovery import checkonu

def test_get_ignored_params():
    """
    Testa a nova função get_ignored_params
    """
    print("=== Testando get_ignored_params ===")
    
    # Teste com serial fictício
    result = get_ignored_params(serial="TEST123456")
    print(f"Teste serial TEST123456: {result}")
    
    # Teste com username fictício  
    result = get_ignored_params(username="test_user")
    print(f"Teste username test_user: {result}")
    
    # Teste sem parâmetros
    result = get_ignored_params()
    print(f"Teste sem parâmetros: {result}")
    
    print()

def test_compatibility():
    """
    Testa se a função antiga ainda funciona para compatibilidade
    """
    print("=== Testando compatibilidade com checkignorelist ===")
    
    # Teste da função antiga
    result = checkignorelist(serial="TEST123456")
    print(f"checkignorelist(serial='TEST123456'): {result}")
    
    result = checkignorelist(serial="TEST123456", comodato=1)
    print(f"checkignorelist(serial='TEST123456', comodato=1): {result}")
    
    print()

def test_checkonu_performance():
    """
    Testa a performance da nova implementação do checkonu
    """
    print("=== Testando checkonu com nova implementação ===")
    
    # ONU de teste
    onu_test = {
        'serial': 'TEST123456',
        'username': 'test_user',
        'patrimonio': '12345',
        'modelo': 'ZTE F670L',
        'mac': '00:11:22:33:44:55',
        'login_ativo': 'S',
        'plano': 'PLANO_100MB'
    }
    
    try:
        missing_params = checkonu(onu_test)
        print(f"Missing params para ONU teste: {missing_params}")
    except Exception as e:
        print(f"Erro ao testar checkonu: {e}")
    
    print()

if __name__ == "__main__":
    print("Iniciando testes da refatoração...")
    print()
    
    try:
        test_get_ignored_params()
        test_compatibility()
        test_checkonu_performance()
        
        print("Testes concluídos!")
        
    except Exception as e:
        print(f"Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()
