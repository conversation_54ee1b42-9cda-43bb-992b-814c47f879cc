import redis
from rq.job import Job
from rq.registry import StartedJobRegistry, FinishedJobRegistry, FailedJobRegistry
from rq.queue import Queue
from dotenv import load_dotenv
import json
import os

load_dotenv()

redis_host = os.getenv('REDIS_HOST', 'localhost')
redis_port = os.getenv('REDIS_PORT', 6379)
redis_pass = os.getenv('REDIS_PASS', 'admin')

conn = redis.Redis(
    host=redis_host,
    port=redis_port,
    password=redis_pass)

# Fila usada nos seus jobs
queue_name = "olt-*************"
q = Queue(queue_name, connection=conn)

# Substitua com o provision_id que você quer investigar
provision_id = "f7f3366d-918f-4e21-b51b-816f8904ec99"

def diagnose_jobs_by_provision_id(provision_id):
    print(f"\nDiagnostico de jobs para provision_id: {provision_id}\n")

    jobs = q.jobs  # lista de Job instances na fila

    found = False

    for job in jobs:
        try:
            job_args = job.args or []
            job_kwargs = job.kwargs or {}

            print(job_kwargs)

            if job_kwargs.get("provision_id") == provision_id:
                found = True
                status = job.get_status()
                depends_on = job.dependency.id if job.dependency else None

                print(f"Job ID: {job.id}")
                print(f"Funcao: {job.func_name}")
                print(f"Status: {status}")
                print(f"Depends on: {depends_on}")
                if depends_on:
                    parent = Job.fetch(depends_on, connection=conn)
                    print(f"Parent status: {parent.get_status()}")
                print("-" * 40)
        except Exception as e:
            print(f"Erro ao processar job: {e}")

    if not found:
        print("Nenhum job encontrado com esse provision_id.")

# Executar
if __name__ == "__main__":
    diagnose_jobs_by_provision_id(provision_id)
    #diagnose_jobs(provision_id)